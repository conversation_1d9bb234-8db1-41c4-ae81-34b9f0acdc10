#!/bin/bash

# ─────────────────────────────────────────────
# AI Voice Mate - Universal Stop Script
# Stops Python backend and React frontend cleanly
# Supports fallback, port cleanup, orphan detection
# Author: ChatGPT (world-best solution)
# ─────────────────────────────────────────────

set -e

# ───── Color Codes ─────
RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'

# ───── Paths ─────
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_DIR="$SCRIPT_DIR/.pids"
BACKEND_PID_FILE="$PID_DIR/backend.pid"
FRONTEND_PID_FILE="$PID_DIR/frontend.pid"

# ───── Print <PERSON> ─────
log() { echo -e "${GREEN}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
err() { echo -e "${RED}[ERROR]${NC} $1"; }
head() { echo -e "${BLUE}[AI Voice Mate]${NC} $1"; }

# ───── Kill a Full Process Tree ─────
kill_tree() {
    local pid=$1
    if ps -p "$pid" > /dev/null 2>&1; then
        pkill -TERM -P "$pid" || true
        kill -TERM "$pid" 2>/dev/null || true
        sleep 2
        if ps -p "$pid" > /dev/null 2>&1; then
            warn "Force killing PID $pid"
            pkill -KILL -P "$pid" || true
            kill -KILL "$pid" 2>/dev/null || true
        fi
    fi
}

# ───── Stop via PID File ─────
stop_via_pid() {
    local pid_file=$1
    local name=$2

    if [[ -f "$pid_file" ]]; then
        local pid
        pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            log "Stopping $name (PID: $pid)"
            kill_tree "$pid"
        else
            warn "$name PID file exists but process not running"
        fi
        rm -f "$pid_file"
    else
        warn "No PID file for $name"
    fi
}

# ───── Port Cleanup ─────
cleanup_ports() {
    local ports=(3000 5000 4483 5010)
    for port in "${ports[@]}"; do
        local pid
        pid=$(lsof -t -i:$port 2>/dev/null || true)
        if [[ -n "$pid" ]]; then
            warn "Port $port in use by PID $pid"
            kill_tree "$pid"
        fi
    done
}

# ───── Orphaned Process Detection ─────
kill_orphans() {
    local patterns=("python.*main.py" "node.*react-scripts" "npm.*start")
    for pattern in "${patterns[@]}"; do
        local pids
        pids=$(pgrep -f "$pattern" || true)
        for pid in $pids; do
            warn "Killing orphaned process PID $pid (matched: $pattern)"
            kill_tree "$pid"
        done
    done
}

# ───── Temporary File Cleanup ─────
cleanup_files() {
    log "Cleaning up PID files and temp files"
    rm -f "$BACKEND_PID_FILE" "$FRONTEND_PID_FILE"

    [[ -d "$PID_DIR" && -z "$(ls -A "$PID_DIR")" ]] && rmdir "$PID_DIR"
    [[ -d "$SCRIPT_DIR/frontend/.tmp" ]] && rm -rf "$SCRIPT_DIR/frontend/.tmp"
}

# ───── Status Display ─────
status() {
    echo ""
    head "Status Check After Stop"

    if [[ -f "$BACKEND_PID_FILE" ]]; then
        err "✗ Backend may still be running (PID: $(cat "$BACKEND_PID_FILE"))"
    else
        log "✓ Backend stopped"
    fi

    if [[ -f "$FRONTEND_PID_FILE" ]]; then
        err "✗ Frontend may still be running (PID: $(cat "$FRONTEND_PID_FILE"))"
    else
        log "✓ Frontend stopped"
    fi

    local leftovers
    leftovers=$(pgrep -f "main.py|react-scripts|npm start" || true)
    if [[ -n "$leftovers" ]]; then
        warn "Remaining processes: $leftovers"
    fi
    echo ""
}

# ───── Main Execution ─────
main() {
    head "Stopping AI Voice Mate"

    stop_via_pid "$BACKEND_PID_FILE" "Backend"
    stop_via_pid "$FRONTEND_PID_FILE" "Frontend"

    kill_orphans
    cleanup_ports
    cleanup_files
    status

    head "✅ AI Voice Mate stopped successfully!"
}

trap 'err "Interrupted"; exit 1' INT TERM
main "$@"
