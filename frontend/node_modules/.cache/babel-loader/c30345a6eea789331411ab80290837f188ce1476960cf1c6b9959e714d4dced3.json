{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport { Settings, Headphones, Wifi } from 'lucide-react';\nimport { useVoiceSocket } from './hooks/useVoiceSocket';\nimport { useMicrophone } from './hooks/useMicrophone';\nimport CallControls from './components/CallControls';\nimport CallStatus from './components/CallStatus';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [wsUrl, setWsUrl] = useState('ws://localhost:5010');\n  const [showSettings, setShowSettings] = useState(false);\n  const [autoConnect, setAutoConnect] = useState(true);\n  const hasAutoConnectedRef = useRef(false);\n\n  // Initialize WebSocket connection\n  const {\n    connectionState,\n    callState,\n    streamId,\n    sessionId,\n    connect,\n    disconnect,\n    startCall,\n    endCall,\n    sendAudioData,\n    isConnected,\n    isInCall,\n    isConnecting\n  } = useVoiceSocket(wsUrl);\n\n  // Initialize microphone with audio data callback\n  const handleAudioData = useCallback(audioData => {\n    if (isInCall && audioData) {\n      sendAudioData(audioData);\n    }\n  }, [isInCall, sendAudioData]);\n  const {\n    micState,\n    isMuted,\n    audioLevel,\n    isGranted: isMicGranted,\n    isDenied: isMicDenied,\n    isRequesting: isMicRequesting,\n    hasError: hasMicError,\n    requestMicrophone,\n    stopMicrophone,\n    toggleMute\n  } = useMicrophone({\n    onAudioData: handleAudioData,\n    enabled: isInCall\n  });\n\n  // Auto-connect on mount\n  useEffect(() => {\n    if (autoConnect) {\n      connect();\n    }\n  }, [autoConnect]); // Removed connect from dependencies to prevent reconnection loops\n\n  // Handle connection toggle\n  const handleConnectionToggle = () => {\n    if (isConnected) {\n      disconnect();\n    } else {\n      connect();\n    }\n  };\n\n  // Handle call start\n  const handleStartCall = async () => {\n    if (!isMicGranted) {\n      await requestMicrophone();\n    }\n    startCall();\n  };\n\n  // Handle call end\n  const handleEndCall = () => {\n    endCall();\n    stopMicrophone();\n  };\n\n  // Handle mute toggle\n  const handleToggleMute = () => {\n    toggleMute();\n  };\n\n  // Settings panel\n  const SettingsPanel = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card p-4 mb-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900\",\n        children: \"Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowSettings(false),\n        className: \"text-gray-500 hover:text-gray-700\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"WebSocket URL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: wsUrl,\n          onChange: e => setWsUrl(e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n          placeholder: \"ws://localhost:5010\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          id: \"autoConnect\",\n          checked: autoConnect,\n          onChange: e => setAutoConnect(e.target.checked),\n          className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"autoConnect\",\n          className: \"ml-2 block text-sm text-gray-900\",\n          children: \"Auto-connect on page load\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center h-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-primary-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Headphones, {\n                className: \"h-6 w-6 text-primary-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"AI Voice Mate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Real-time Voice Calling\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Wifi, {\n                size: 16,\n                className: isConnected ? 'text-success-600' : 'text-gray-400'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm ${isConnected ? 'text-success-600' : 'text-gray-500'}`,\n                children: isConnected ? 'Connected' : 'Disconnected'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSettings(!showSettings),\n              className: \"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [showSettings && /*#__PURE__*/_jsxDEV(SettingsPanel, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 26\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-4 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Server Connection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: isConnected ? `Connected to ${wsUrl}` : `Disconnected from ${wsUrl}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleConnectionToggle,\n            disabled: isConnecting,\n            className: `px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${isConnected ? 'bg-danger-600 hover:bg-danger-700 text-white focus:ring-danger-500' : 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500'} ${isConnecting ? 'opacity-50 cursor-not-allowed' : ''}`,\n            children: isConnecting ? 'Connecting...' : isConnected ? 'Disconnect' : 'Connect'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-1 lg:order-1\",\n          children: /*#__PURE__*/_jsxDEV(CallControls, {\n            callState: callState,\n            isConnected: isConnected,\n            isMuted: isMuted,\n            onStartCall: handleStartCall,\n            onEndCall: handleEndCall,\n            onToggleMute: handleToggleMute,\n            audioLevel: audioLevel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-2 lg:order-2\",\n          children: /*#__PURE__*/_jsxDEV(CallStatus, {\n            connectionState: connectionState,\n            callState: callState,\n            micState: micState,\n            sessionId: sessionId,\n            streamId: streamId,\n            audioLevel: audioLevel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), isMicDenied && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 card p-4 bg-yellow-50 border-yellow-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 16,\n                className: \"text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-yellow-800\",\n              children: \"Microphone Access Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-yellow-700 mt-1\",\n              children: \"To make voice calls, please allow microphone access in your browser settings and refresh the page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: requestMicrophone,\n              className: \"mt-2 text-sm text-yellow-800 underline hover:text-yellow-900\",\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this), hasMicError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 card p-4 bg-danger-50 border-danger-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-danger-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 16,\n                className: \"text-danger-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-danger-800\",\n              children: \"Microphone Error\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-danger-700 mt-1\",\n              children: \"There was an error accessing your microphone. Please check your device settings and try again.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 card p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"How to Use\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3 text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Ensure you're connected to the WebSocket server (green status indicator)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Allow microphone access when prompted by your browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Click the green phone button to start a voice call\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Use the microphone and speaker controls during the call\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Click the red phone button to end the call\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\",\n      toastOptions: {\n        duration: 4000,\n        style: {\n          background: '#363636',\n          color: '#fff'\n        },\n        success: {\n          duration: 3000,\n          iconTheme: {\n            primary: '#10b981',\n            secondary: '#fff'\n          }\n        },\n        error: {\n          duration: 5000,\n          iconTheme: {\n            primary: '#ef4444',\n            secondary: '#fff'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"KtGevgriaLPDS2EOIGDlxaSkrrc=\", false, function () {\n  return [useVoiceSocket, useMicrophone];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Toaster", "Settings", "Headphones", "Wifi", "useVoiceSocket", "useMicrophone", "CallControls", "CallStatus", "jsxDEV", "_jsxDEV", "App", "_s", "wsUrl", "setWsUrl", "showSettings", "setShowSettings", "autoConnect", "setAutoConnect", "hasAutoConnectedRef", "connectionState", "callState", "streamId", "sessionId", "connect", "disconnect", "startCall", "endCall", "sendAudioData", "isConnected", "isInCall", "isConnecting", "handleAudioData", "audioData", "micState", "isMuted", "audioLevel", "isGranted", "isMicGranted", "isDenied", "isMicDenied", "isRequesting", "isMicRequesting", "<PERSON><PERSON><PERSON><PERSON>", "hasMicError", "requestMicrophone", "stopMicrophone", "toggleMute", "onAudioData", "enabled", "handleConnectionToggle", "handleStartCall", "handleEndCall", "handleToggleMute", "SettingsPanel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "value", "onChange", "e", "target", "placeholder", "id", "checked", "htmlFor", "size", "disabled", "onStartCall", "onEndCall", "onToggleMute", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport { Settings, Headphones, Wifi } from 'lucide-react';\n\nimport { useVoiceSocket } from './hooks/useVoiceSocket';\nimport { useMicrophone } from './hooks/useMicrophone';\nimport CallControls from './components/CallControls';\nimport CallStatus from './components/CallStatus';\n\nfunction App() {\n  const [wsUrl, setWsUrl] = useState('ws://localhost:5010');\n  const [showSettings, setShowSettings] = useState(false);\n  const [autoConnect, setAutoConnect] = useState(true);\n  const hasAutoConnectedRef = useRef(false);\n\n  // Initialize WebSocket connection\n  const {\n    connectionState,\n    callState,\n    streamId,\n    sessionId,\n    connect,\n    disconnect,\n    startCall,\n    endCall,\n    sendAudioData,\n    isConnected,\n    isInCall,\n    isConnecting\n  } = useVoiceSocket(wsUrl);\n\n  // Initialize microphone with audio data callback\n  const handleAudioData = useCallback((audioData) => {\n    if (isInCall && audioData) {\n      sendAudioData(audioData);\n    }\n  }, [isInCall, sendAudioData]);\n\n  const {\n    micState,\n    isMuted,\n    audioLevel,\n    isGranted: isMicGranted,\n    isDenied: isMicDenied,\n    isRequesting: isMicRequesting,\n    hasError: hasMicError,\n    requestMicrophone,\n    stopMicrophone,\n    toggleMute\n  } = useMicrophone({ \n    onAudioData: handleAudioData, \n    enabled: isInCall \n  });\n\n  // Auto-connect on mount\n  useEffect(() => {\n    if (autoConnect) {\n      connect();\n    }\n  }, [autoConnect]); // Removed connect from dependencies to prevent reconnection loops\n\n  // Handle connection toggle\n  const handleConnectionToggle = () => {\n    if (isConnected) {\n      disconnect();\n    } else {\n      connect();\n    }\n  };\n\n  // Handle call start\n  const handleStartCall = async () => {\n    if (!isMicGranted) {\n      await requestMicrophone();\n    }\n    startCall();\n  };\n\n  // Handle call end\n  const handleEndCall = () => {\n    endCall();\n    stopMicrophone();\n  };\n\n  // Handle mute toggle\n  const handleToggleMute = () => {\n    toggleMute();\n  };\n\n  // Settings panel\n  const SettingsPanel = () => (\n    <div className=\"card p-4 mb-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Settings</h3>\n        <button\n          onClick={() => setShowSettings(false)}\n          className=\"text-gray-500 hover:text-gray-700\"\n        >\n          ×\n        </button>\n      </div>\n      \n      <div className=\"space-y-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            WebSocket URL\n          </label>\n          <input\n            type=\"text\"\n            value={wsUrl}\n            onChange={(e) => setWsUrl(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            placeholder=\"ws://localhost:5010\"\n          />\n        </div>\n        \n        <div className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            id=\"autoConnect\"\n            checked={autoConnect}\n            onChange={(e) => setAutoConnect(e.target.checked)}\n            className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n          />\n          <label htmlFor=\"autoConnect\" className=\"ml-2 block text-sm text-gray-900\">\n            Auto-connect on page load\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-primary-100 rounded-lg\">\n                <Headphones className=\"h-6 w-6 text-primary-600\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">AI Voice Mate</h1>\n                <p className=\"text-sm text-gray-500\">Real-time Voice Calling</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-3\">\n              {/* Connection Status Indicator */}\n              <div className=\"flex items-center space-x-2\">\n                <Wifi \n                  size={16} \n                  className={isConnected ? 'text-success-600' : 'text-gray-400'} \n                />\n                <span className={`text-sm ${isConnected ? 'text-success-600' : 'text-gray-500'}`}>\n                  {isConnected ? 'Connected' : 'Disconnected'}\n                </span>\n              </div>\n              \n              {/* Settings Button */}\n              <button\n                onClick={() => setShowSettings(!showSettings)}\n                className=\"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\"\n              >\n                <Settings size={20} />\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Settings Panel */}\n        {showSettings && <SettingsPanel />}\n\n        {/* Connection Controls */}\n        <div className=\"card p-4 mb-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">Server Connection</h3>\n              <p className=\"text-sm text-gray-500\">\n                {isConnected ? `Connected to ${wsUrl}` : `Disconnected from ${wsUrl}`}\n              </p>\n            </div>\n            <button\n              onClick={handleConnectionToggle}\n              disabled={isConnecting}\n              className={`px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${\n                isConnected\n                  ? 'bg-danger-600 hover:bg-danger-700 text-white focus:ring-danger-500'\n                  : 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500'\n              } ${isConnecting ? 'opacity-50 cursor-not-allowed' : ''}`}\n            >\n              {isConnecting ? 'Connecting...' : isConnected ? 'Disconnect' : 'Connect'}\n            </button>\n          </div>\n        </div>\n\n        {/* Main Interface Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Call Controls */}\n          <div className=\"order-1 lg:order-1\">\n            <CallControls\n              callState={callState}\n              isConnected={isConnected}\n              isMuted={isMuted}\n              onStartCall={handleStartCall}\n              onEndCall={handleEndCall}\n              onToggleMute={handleToggleMute}\n              audioLevel={audioLevel}\n            />\n          </div>\n\n          {/* Call Status */}\n          <div className=\"order-2 lg:order-2\">\n            <CallStatus\n              connectionState={connectionState}\n              callState={callState}\n              micState={micState}\n              sessionId={sessionId}\n              streamId={streamId}\n              audioLevel={audioLevel}\n            />\n          </div>\n        </div>\n\n        {/* Microphone Permissions Notice */}\n        {isMicDenied && (\n          <div className=\"mt-6 card p-4 bg-yellow-50 border-yellow-200\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\">\n                  <Settings size={16} className=\"text-yellow-600\" />\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-yellow-800\">\n                  Microphone Access Required\n                </h3>\n                <p className=\"text-sm text-yellow-700 mt-1\">\n                  To make voice calls, please allow microphone access in your browser settings and refresh the page.\n                </p>\n                <button\n                  onClick={requestMicrophone}\n                  className=\"mt-2 text-sm text-yellow-800 underline hover:text-yellow-900\"\n                >\n                  Try Again\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Error States */}\n        {hasMicError && (\n          <div className=\"mt-6 card p-4 bg-danger-50 border-danger-200\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-danger-100 rounded-full flex items-center justify-center\">\n                  <Settings size={16} className=\"text-danger-600\" />\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-danger-800\">\n                  Microphone Error\n                </h3>\n                <p className=\"text-sm text-danger-700 mt-1\">\n                  There was an error accessing your microphone. Please check your device settings and try again.\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Instructions */}\n        <div className=\"mt-8 card p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">How to Use</h3>\n          <div className=\"space-y-3 text-sm text-gray-600\">\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">1</span>\n              <p>Ensure you're connected to the WebSocket server (green status indicator)</p>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">2</span>\n              <p>Allow microphone access when prompted by your browser</p>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">3</span>\n              <p>Click the green phone button to start a voice call</p>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">4</span>\n              <p>Use the microphone and speaker controls during the call</p>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">5</span>\n              <p>Click the red phone button to end the call</p>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Toast Notifications */}\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: '#363636',\n            color: '#fff',\n          },\n          success: {\n            duration: 3000,\n            iconTheme: {\n              primary: '#10b981',\n              secondary: '#fff',\n            },\n          },\n          error: {\n            duration: 5000,\n            iconTheme: {\n              primary: '#ef4444',\n              secondary: '#fff',\n            },\n          },\n        }}\n      />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,QAAQ,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAEzD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,qBAAqB,CAAC;EACzD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMsB,mBAAmB,GAAGnB,MAAM,CAAC,KAAK,CAAC;;EAEzC;EACA,MAAM;IACJoB,eAAe;IACfC,SAAS;IACTC,QAAQ;IACRC,SAAS;IACTC,OAAO;IACPC,UAAU;IACVC,SAAS;IACTC,OAAO;IACPC,aAAa;IACbC,WAAW;IACXC,QAAQ;IACRC;EACF,CAAC,GAAG1B,cAAc,CAACQ,KAAK,CAAC;;EAEzB;EACA,MAAMmB,eAAe,GAAGjC,WAAW,CAAEkC,SAAS,IAAK;IACjD,IAAIH,QAAQ,IAAIG,SAAS,EAAE;MACzBL,aAAa,CAACK,SAAS,CAAC;IAC1B;EACF,CAAC,EAAE,CAACH,QAAQ,EAAEF,aAAa,CAAC,CAAC;EAE7B,MAAM;IACJM,QAAQ;IACRC,OAAO;IACPC,UAAU;IACVC,SAAS,EAAEC,YAAY;IACvBC,QAAQ,EAAEC,WAAW;IACrBC,YAAY,EAAEC,eAAe;IAC7BC,QAAQ,EAAEC,WAAW;IACrBC,iBAAiB;IACjBC,cAAc;IACdC;EACF,CAAC,GAAGzC,aAAa,CAAC;IAChB0C,WAAW,EAAEhB,eAAe;IAC5BiB,OAAO,EAAEnB;EACX,CAAC,CAAC;;EAEF;EACAhC,SAAS,CAAC,MAAM;IACd,IAAImB,WAAW,EAAE;MACfO,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACP,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEnB;EACA,MAAMiC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIrB,WAAW,EAAE;MACfJ,UAAU,CAAC,CAAC;IACd,CAAC,MAAM;MACLD,OAAO,CAAC,CAAC;IACX;EACF,CAAC;;EAED;EACA,MAAM2B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACb,YAAY,EAAE;MACjB,MAAMO,iBAAiB,CAAC,CAAC;IAC3B;IACAnB,SAAS,CAAC,CAAC;EACb,CAAC;;EAED;EACA,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1BzB,OAAO,CAAC,CAAC;IACTmB,cAAc,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAGA,CAAA,KAAM;IAC7BN,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMO,aAAa,GAAGA,CAAA,kBACpB5C,OAAA;IAAK6C,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5B9C,OAAA;MAAK6C,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD9C,OAAA;QAAI6C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjElD,OAAA;QACEmD,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,KAAK,CAAE;QACtCuC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC9C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENlD,OAAA;MAAK6C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB9C,OAAA;QAAA8C,QAAA,gBACE9C,OAAA;UAAO6C,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRlD,OAAA;UACEoD,IAAI,EAAC,MAAM;UACXC,KAAK,EAAElD,KAAM;UACbmD,QAAQ,EAAGC,CAAC,IAAKnD,QAAQ,CAACmD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC1CR,SAAS,EAAC,oIAAoI;UAC9IY,WAAW,EAAC;QAAqB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENlD,OAAA;QAAK6C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC9C,OAAA;UACEoD,IAAI,EAAC,UAAU;UACfM,EAAE,EAAC,aAAa;UAChBC,OAAO,EAAEpD,WAAY;UACrB+C,QAAQ,EAAGC,CAAC,IAAK/C,cAAc,CAAC+C,CAAC,CAACC,MAAM,CAACG,OAAO,CAAE;UAClDd,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACFlD,OAAA;UAAO4D,OAAO,EAAC,aAAa;UAACf,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACElD,OAAA;IAAK6C,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtC9C,OAAA;MAAQ6C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC7D9C,OAAA;QAAK6C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD9C,OAAA;UAAK6C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD9C,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C9C,OAAA;cAAK6C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,eAC5C9C,OAAA,CAACP,UAAU;gBAACoD,SAAS,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNlD,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAI6C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClElD,OAAA;gBAAG6C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1C9C,OAAA;cAAK6C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9C,OAAA,CAACN,IAAI;gBACHmE,IAAI,EAAE,EAAG;gBACThB,SAAS,EAAE1B,WAAW,GAAG,kBAAkB,GAAG;cAAgB;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACFlD,OAAA;gBAAM6C,SAAS,EAAE,WAAW1B,WAAW,GAAG,kBAAkB,GAAG,eAAe,EAAG;gBAAA2B,QAAA,EAC9E3B,WAAW,GAAG,WAAW,GAAG;cAAc;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNlD,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CwC,SAAS,EAAC,sFAAsF;cAAAC,QAAA,eAEhG9C,OAAA,CAACR,QAAQ;gBAACqE,IAAI,EAAE;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTlD,OAAA;MAAM6C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,GAE1DzC,YAAY,iBAAIL,OAAA,CAAC4C,aAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGlClD,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B9C,OAAA;UAAK6C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD9C,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAI6C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1ElD,OAAA;cAAG6C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACjC3B,WAAW,GAAG,gBAAgBhB,KAAK,EAAE,GAAG,qBAAqBA,KAAK;YAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNlD,OAAA;YACEmD,OAAO,EAAEX,sBAAuB;YAChCsB,QAAQ,EAAEzC,YAAa;YACvBwB,SAAS,EAAE,0GACT1B,WAAW,GACP,oEAAoE,GACpE,uEAAuE,IACzEE,YAAY,GAAG,+BAA+B,GAAG,EAAE,EAAG;YAAAyB,QAAA,EAEzDzB,YAAY,GAAG,eAAe,GAAGF,WAAW,GAAG,YAAY,GAAG;UAAS;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD9C,OAAA;UAAK6C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC9C,OAAA,CAACH,YAAY;YACXc,SAAS,EAAEA,SAAU;YACrBQ,WAAW,EAAEA,WAAY;YACzBM,OAAO,EAAEA,OAAQ;YACjBsC,WAAW,EAAEtB,eAAgB;YAC7BuB,SAAS,EAAEtB,aAAc;YACzBuB,YAAY,EAAEtB,gBAAiB;YAC/BjB,UAAU,EAAEA;UAAW;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlD,OAAA;UAAK6C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC9C,OAAA,CAACF,UAAU;YACTY,eAAe,EAAEA,eAAgB;YACjCC,SAAS,EAAEA,SAAU;YACrBa,QAAQ,EAAEA,QAAS;YACnBX,SAAS,EAAEA,SAAU;YACrBD,QAAQ,EAAEA,QAAS;YACnBc,UAAU,EAAEA;UAAW;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLpB,WAAW,iBACV9B,OAAA;QAAK6C,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D9C,OAAA;UAAK6C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC9C,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B9C,OAAA;cAAK6C,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClF9C,OAAA,CAACR,QAAQ;gBAACqE,IAAI,EAAE,EAAG;gBAAChB,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAI6C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlD,OAAA;cAAG6C,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAE5C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlD,OAAA;cACEmD,OAAO,EAAEhB,iBAAkB;cAC3BU,SAAS,EAAC,8DAA8D;cAAAC,QAAA,EACzE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAhB,WAAW,iBACVlC,OAAA;QAAK6C,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D9C,OAAA;UAAK6C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC9C,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B9C,OAAA;cAAK6C,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClF9C,OAAA,CAACR,QAAQ;gBAACqE,IAAI,EAAE,EAAG;gBAAChB,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAI6C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlD,OAAA;cAAG6C,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAE5C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDlD,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B9C,OAAA;UAAI6C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxElD,OAAA;UAAK6C,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9C9C,OAAA;YAAK6C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC9C,OAAA;cAAM6C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJlD,OAAA;cAAA8C,QAAA,EAAG;YAAwE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eACNlD,OAAA;YAAK6C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC9C,OAAA;cAAM6C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJlD,OAAA;cAAA8C,QAAA,EAAG;YAAqD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNlD,OAAA;YAAK6C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC9C,OAAA;cAAM6C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJlD,OAAA;cAAA8C,QAAA,EAAG;YAAkD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNlD,OAAA;YAAK6C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC9C,OAAA;cAAM6C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJlD,OAAA;cAAA8C,QAAA,EAAG;YAAuD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNlD,OAAA;YAAK6C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC9C,OAAA;cAAM6C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJlD,OAAA;cAAA8C,QAAA,EAAG;YAA0C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPlD,OAAA,CAACT,OAAO;MACN2E,QAAQ,EAAC,WAAW;MACpBC,YAAY,EAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;UACLC,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE;QACT,CAAC;QACDC,OAAO,EAAE;UACPJ,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF,CAAC;QACDC,KAAK,EAAE;UACLR,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF;MACF;IAAE;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAChD,EAAA,CAlUQD,GAAG;EAAA,QAoBNN,cAAc,EAoBdC,aAAa;AAAA;AAAAiF,EAAA,GAxCV5E,GAAG;AAoUZ,eAAeA,GAAG;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}