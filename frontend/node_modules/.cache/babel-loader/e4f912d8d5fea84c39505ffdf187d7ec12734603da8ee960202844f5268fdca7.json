{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport { Settings, Headphones, Wifi } from 'lucide-react';\nimport { useVoiceSocket } from './hooks/useVoiceSocket';\nimport { useMicrophone } from './hooks/useMicrophone';\nimport CallControls from './components/CallControls';\nimport CallStatus from './components/CallStatus';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [wsUrl, setWsUrl] = useState('ws://localhost:5010');\n  const [showSettings, setShowSettings] = useState(false);\n  const [autoConnect, setAutoConnect] = useState(true);\n\n  // Initialize WebSocket connection\n  const {\n    connectionState,\n    callState,\n    streamId,\n    sessionId,\n    connect,\n    disconnect,\n    startCall,\n    endCall,\n    sendAudioData,\n    isConnected,\n    isInCall,\n    isConnecting\n  } = useVoiceSocket(wsUrl);\n\n  // Initialize microphone with audio data callback\n  const handleAudioData = useCallback(audioData => {\n    if (isInCall && audioData) {\n      sendAudioData(audioData);\n    }\n  }, [isInCall, sendAudioData]);\n  const {\n    micState,\n    isMuted,\n    audioLevel,\n    isGranted: isMicGranted,\n    isDenied: isMicDenied,\n    isRequesting: isMicRequesting,\n    hasError: hasMicError,\n    requestMicrophone,\n    stopMicrophone,\n    toggleMute\n  } = useMicrophone({\n    onAudioData: handleAudioData,\n    enabled: isInCall\n  });\n\n  // Auto-connect on mount\n  useEffect(() => {\n    if (autoConnect) {\n      connect();\n    }\n  }, [autoConnect]); // Removed connect from dependencies to prevent reconnection loops\n\n  // Handle connection toggle\n  const handleConnectionToggle = () => {\n    if (isConnected) {\n      disconnect();\n    } else {\n      connect();\n    }\n  };\n\n  // Handle call start\n  const handleStartCall = async () => {\n    if (!isMicGranted) {\n      await requestMicrophone();\n    }\n    startCall();\n  };\n\n  // Handle call end\n  const handleEndCall = () => {\n    endCall();\n    stopMicrophone();\n  };\n\n  // Handle mute toggle\n  const handleToggleMute = () => {\n    toggleMute();\n  };\n\n  // Settings panel\n  const SettingsPanel = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card p-4 mb-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900\",\n        children: \"Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowSettings(false),\n        className: \"text-gray-500 hover:text-gray-700\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"WebSocket URL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: wsUrl,\n          onChange: e => setWsUrl(e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n          placeholder: \"ws://localhost:5010\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          id: \"autoConnect\",\n          checked: autoConnect,\n          onChange: e => setAutoConnect(e.target.checked),\n          className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"autoConnect\",\n          className: \"ml-2 block text-sm text-gray-900\",\n          children: \"Auto-connect on page load\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center h-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-primary-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Headphones, {\n                className: \"h-6 w-6 text-primary-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold text-gray-900\",\n                children: \"AI Voice Mate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Real-time Voice Calling\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Wifi, {\n                size: 16,\n                className: isConnected ? 'text-success-600' : 'text-gray-400'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm ${isConnected ? 'text-success-600' : 'text-gray-500'}`,\n                children: isConnected ? 'Connected' : 'Disconnected'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSettings(!showSettings),\n              className: \"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [showSettings && /*#__PURE__*/_jsxDEV(SettingsPanel, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 26\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card p-4 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Server Connection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: isConnected ? `Connected to ${wsUrl}` : `Disconnected from ${wsUrl}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleConnectionToggle,\n            disabled: isConnecting,\n            className: `px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${isConnected ? 'bg-danger-600 hover:bg-danger-700 text-white focus:ring-danger-500' : 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500'} ${isConnecting ? 'opacity-50 cursor-not-allowed' : ''}`,\n            children: isConnecting ? 'Connecting...' : isConnected ? 'Disconnect' : 'Connect'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-1 lg:order-1\",\n          children: /*#__PURE__*/_jsxDEV(CallControls, {\n            callState: callState,\n            isConnected: isConnected,\n            isMuted: isMuted,\n            onStartCall: handleStartCall,\n            onEndCall: handleEndCall,\n            onToggleMute: handleToggleMute,\n            audioLevel: audioLevel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-2 lg:order-2\",\n          children: /*#__PURE__*/_jsxDEV(CallStatus, {\n            connectionState: connectionState,\n            callState: callState,\n            micState: micState,\n            sessionId: sessionId,\n            streamId: streamId,\n            audioLevel: audioLevel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), isMicDenied && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 card p-4 bg-yellow-50 border-yellow-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 16,\n                className: \"text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-yellow-800\",\n              children: \"Microphone Access Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-yellow-700 mt-1\",\n              children: \"To make voice calls, please allow microphone access in your browser settings and refresh the page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: requestMicrophone,\n              className: \"mt-2 text-sm text-yellow-800 underline hover:text-yellow-900\",\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this), hasMicError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 card p-4 bg-danger-50 border-danger-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-danger-100 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 16,\n                className: \"text-danger-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-danger-800\",\n              children: \"Microphone Error\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-danger-700 mt-1\",\n              children: \"There was an error accessing your microphone. Please check your device settings and try again.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 card p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"How to Use\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3 text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Ensure you're connected to the WebSocket server (green status indicator)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Allow microphone access when prompted by your browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Click the green phone button to start a voice call\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Use the microphone and speaker controls during the call\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\",\n              children: \"5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Click the red phone button to end the call\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\",\n      toastOptions: {\n        duration: 4000,\n        style: {\n          background: '#363636',\n          color: '#fff'\n        },\n        success: {\n          duration: 3000,\n          iconTheme: {\n            primary: '#10b981',\n            secondary: '#fff'\n          }\n        },\n        error: {\n          duration: 5000,\n          iconTheme: {\n            primary: '#ef4444',\n            secondary: '#fff'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"prESU3g4ABv13xdomOzsH2vzNvE=\", false, function () {\n  return [useVoiceSocket, useMicrophone];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Toaster", "Settings", "Headphones", "Wifi", "useVoiceSocket", "useMicrophone", "CallControls", "CallStatus", "jsxDEV", "_jsxDEV", "App", "_s", "wsUrl", "setWsUrl", "showSettings", "setShowSettings", "autoConnect", "setAutoConnect", "connectionState", "callState", "streamId", "sessionId", "connect", "disconnect", "startCall", "endCall", "sendAudioData", "isConnected", "isInCall", "isConnecting", "handleAudioData", "audioData", "micState", "isMuted", "audioLevel", "isGranted", "isMicGranted", "isDenied", "isMicDenied", "isRequesting", "isMicRequesting", "<PERSON><PERSON><PERSON><PERSON>", "hasMicError", "requestMicrophone", "stopMicrophone", "toggleMute", "onAudioData", "enabled", "handleConnectionToggle", "handleStartCall", "handleEndCall", "handleToggleMute", "SettingsPanel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "value", "onChange", "e", "target", "placeholder", "id", "checked", "htmlFor", "size", "disabled", "onStartCall", "onEndCall", "onToggleMute", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport { Settings, Headphones, Wifi } from 'lucide-react';\n\nimport { useVoiceSocket } from './hooks/useVoiceSocket';\nimport { useMicrophone } from './hooks/useMicrophone';\nimport CallControls from './components/CallControls';\nimport CallStatus from './components/CallStatus';\n\nfunction App() {\n  const [wsUrl, setWsUrl] = useState('ws://localhost:5010');\n  const [showSettings, setShowSettings] = useState(false);\n  const [autoConnect, setAutoConnect] = useState(true);\n\n  // Initialize WebSocket connection\n  const {\n    connectionState,\n    callState,\n    streamId,\n    sessionId,\n    connect,\n    disconnect,\n    startCall,\n    endCall,\n    sendAudioData,\n    isConnected,\n    isInCall,\n    isConnecting\n  } = useVoiceSocket(wsUrl);\n\n  // Initialize microphone with audio data callback\n  const handleAudioData = useCallback((audioData) => {\n    if (isInCall && audioData) {\n      sendAudioData(audioData);\n    }\n  }, [isInCall, sendAudioData]);\n\n  const {\n    micState,\n    isMuted,\n    audioLevel,\n    isGranted: isMicGranted,\n    isDenied: isMicDenied,\n    isRequesting: isMicRequesting,\n    hasError: hasMicError,\n    requestMicrophone,\n    stopMicrophone,\n    toggleMute\n  } = useMicrophone({ \n    onAudioData: handleAudioData, \n    enabled: isInCall \n  });\n\n  // Auto-connect on mount\n  useEffect(() => {\n    if (autoConnect) {\n      connect();\n    }\n  }, [autoConnect]); // Removed connect from dependencies to prevent reconnection loops\n\n  // Handle connection toggle\n  const handleConnectionToggle = () => {\n    if (isConnected) {\n      disconnect();\n    } else {\n      connect();\n    }\n  };\n\n  // Handle call start\n  const handleStartCall = async () => {\n    if (!isMicGranted) {\n      await requestMicrophone();\n    }\n    startCall();\n  };\n\n  // Handle call end\n  const handleEndCall = () => {\n    endCall();\n    stopMicrophone();\n  };\n\n  // Handle mute toggle\n  const handleToggleMute = () => {\n    toggleMute();\n  };\n\n  // Settings panel\n  const SettingsPanel = () => (\n    <div className=\"card p-4 mb-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Settings</h3>\n        <button\n          onClick={() => setShowSettings(false)}\n          className=\"text-gray-500 hover:text-gray-700\"\n        >\n          ×\n        </button>\n      </div>\n      \n      <div className=\"space-y-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            WebSocket URL\n          </label>\n          <input\n            type=\"text\"\n            value={wsUrl}\n            onChange={(e) => setWsUrl(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            placeholder=\"ws://localhost:5010\"\n          />\n        </div>\n        \n        <div className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            id=\"autoConnect\"\n            checked={autoConnect}\n            onChange={(e) => setAutoConnect(e.target.checked)}\n            className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n          />\n          <label htmlFor=\"autoConnect\" className=\"ml-2 block text-sm text-gray-900\">\n            Auto-connect on page load\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-primary-100 rounded-lg\">\n                <Headphones className=\"h-6 w-6 text-primary-600\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">AI Voice Mate</h1>\n                <p className=\"text-sm text-gray-500\">Real-time Voice Calling</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-3\">\n              {/* Connection Status Indicator */}\n              <div className=\"flex items-center space-x-2\">\n                <Wifi \n                  size={16} \n                  className={isConnected ? 'text-success-600' : 'text-gray-400'} \n                />\n                <span className={`text-sm ${isConnected ? 'text-success-600' : 'text-gray-500'}`}>\n                  {isConnected ? 'Connected' : 'Disconnected'}\n                </span>\n              </div>\n              \n              {/* Settings Button */}\n              <button\n                onClick={() => setShowSettings(!showSettings)}\n                className=\"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\"\n              >\n                <Settings size={20} />\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Settings Panel */}\n        {showSettings && <SettingsPanel />}\n\n        {/* Connection Controls */}\n        <div className=\"card p-4 mb-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">Server Connection</h3>\n              <p className=\"text-sm text-gray-500\">\n                {isConnected ? `Connected to ${wsUrl}` : `Disconnected from ${wsUrl}`}\n              </p>\n            </div>\n            <button\n              onClick={handleConnectionToggle}\n              disabled={isConnecting}\n              className={`px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${\n                isConnected\n                  ? 'bg-danger-600 hover:bg-danger-700 text-white focus:ring-danger-500'\n                  : 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500'\n              } ${isConnecting ? 'opacity-50 cursor-not-allowed' : ''}`}\n            >\n              {isConnecting ? 'Connecting...' : isConnected ? 'Disconnect' : 'Connect'}\n            </button>\n          </div>\n        </div>\n\n        {/* Main Interface Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Call Controls */}\n          <div className=\"order-1 lg:order-1\">\n            <CallControls\n              callState={callState}\n              isConnected={isConnected}\n              isMuted={isMuted}\n              onStartCall={handleStartCall}\n              onEndCall={handleEndCall}\n              onToggleMute={handleToggleMute}\n              audioLevel={audioLevel}\n            />\n          </div>\n\n          {/* Call Status */}\n          <div className=\"order-2 lg:order-2\">\n            <CallStatus\n              connectionState={connectionState}\n              callState={callState}\n              micState={micState}\n              sessionId={sessionId}\n              streamId={streamId}\n              audioLevel={audioLevel}\n            />\n          </div>\n        </div>\n\n        {/* Microphone Permissions Notice */}\n        {isMicDenied && (\n          <div className=\"mt-6 card p-4 bg-yellow-50 border-yellow-200\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\">\n                  <Settings size={16} className=\"text-yellow-600\" />\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-yellow-800\">\n                  Microphone Access Required\n                </h3>\n                <p className=\"text-sm text-yellow-700 mt-1\">\n                  To make voice calls, please allow microphone access in your browser settings and refresh the page.\n                </p>\n                <button\n                  onClick={requestMicrophone}\n                  className=\"mt-2 text-sm text-yellow-800 underline hover:text-yellow-900\"\n                >\n                  Try Again\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Error States */}\n        {hasMicError && (\n          <div className=\"mt-6 card p-4 bg-danger-50 border-danger-200\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-danger-100 rounded-full flex items-center justify-center\">\n                  <Settings size={16} className=\"text-danger-600\" />\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-medium text-danger-800\">\n                  Microphone Error\n                </h3>\n                <p className=\"text-sm text-danger-700 mt-1\">\n                  There was an error accessing your microphone. Please check your device settings and try again.\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Instructions */}\n        <div className=\"mt-8 card p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">How to Use</h3>\n          <div className=\"space-y-3 text-sm text-gray-600\">\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">1</span>\n              <p>Ensure you're connected to the WebSocket server (green status indicator)</p>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">2</span>\n              <p>Allow microphone access when prompted by your browser</p>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">3</span>\n              <p>Click the green phone button to start a voice call</p>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">4</span>\n              <p>Use the microphone and speaker controls during the call</p>\n            </div>\n            <div className=\"flex items-start space-x-3\">\n              <span className=\"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium\">5</span>\n              <p>Click the red phone button to end the call</p>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Toast Notifications */}\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: '#363636',\n            color: '#fff',\n          },\n          success: {\n            duration: 3000,\n            iconTheme: {\n              primary: '#10b981',\n              secondary: '#fff',\n            },\n          },\n          error: {\n            duration: 5000,\n            iconTheme: {\n              primary: '#ef4444',\n              secondary: '#fff',\n            },\n          },\n        }}\n      />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,QAAQ,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAEzD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,qBAAqB,CAAC;EACzD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM;IACJqB,eAAe;IACfC,SAAS;IACTC,QAAQ;IACRC,SAAS;IACTC,OAAO;IACPC,UAAU;IACVC,SAAS;IACTC,OAAO;IACPC,aAAa;IACbC,WAAW;IACXC,QAAQ;IACRC;EACF,CAAC,GAAGzB,cAAc,CAACQ,KAAK,CAAC;;EAEzB;EACA,MAAMkB,eAAe,GAAG/B,WAAW,CAAEgC,SAAS,IAAK;IACjD,IAAIH,QAAQ,IAAIG,SAAS,EAAE;MACzBL,aAAa,CAACK,SAAS,CAAC;IAC1B;EACF,CAAC,EAAE,CAACH,QAAQ,EAAEF,aAAa,CAAC,CAAC;EAE7B,MAAM;IACJM,QAAQ;IACRC,OAAO;IACPC,UAAU;IACVC,SAAS,EAAEC,YAAY;IACvBC,QAAQ,EAAEC,WAAW;IACrBC,YAAY,EAAEC,eAAe;IAC7BC,QAAQ,EAAEC,WAAW;IACrBC,iBAAiB;IACjBC,cAAc;IACdC;EACF,CAAC,GAAGxC,aAAa,CAAC;IAChByC,WAAW,EAAEhB,eAAe;IAC5BiB,OAAO,EAAEnB;EACX,CAAC,CAAC;;EAEF;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIkB,WAAW,EAAE;MACfM,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACN,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEnB;EACA,MAAMgC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIrB,WAAW,EAAE;MACfJ,UAAU,CAAC,CAAC;IACd,CAAC,MAAM;MACLD,OAAO,CAAC,CAAC;IACX;EACF,CAAC;;EAED;EACA,MAAM2B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACb,YAAY,EAAE;MACjB,MAAMO,iBAAiB,CAAC,CAAC;IAC3B;IACAnB,SAAS,CAAC,CAAC;EACb,CAAC;;EAED;EACA,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1BzB,OAAO,CAAC,CAAC;IACTmB,cAAc,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAGA,CAAA,KAAM;IAC7BN,UAAU,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMO,aAAa,GAAGA,CAAA,kBACpB3C,OAAA;IAAK4C,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5B7C,OAAA;MAAK4C,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD7C,OAAA;QAAI4C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjEjD,OAAA;QACEkD,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,KAAK,CAAE;QACtCsC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAC9C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENjD,OAAA;MAAK4C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB7C,OAAA;QAAA6C,QAAA,gBACE7C,OAAA;UAAO4C,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjD,OAAA;UACEmD,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEjD,KAAM;UACbkD,QAAQ,EAAGC,CAAC,IAAKlD,QAAQ,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC1CR,SAAS,EAAC,oIAAoI;UAC9IY,WAAW,EAAC;QAAqB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjD,OAAA;QAAK4C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7C,OAAA;UACEmD,IAAI,EAAC,UAAU;UACfM,EAAE,EAAC,aAAa;UAChBC,OAAO,EAAEnD,WAAY;UACrB8C,QAAQ,EAAGC,CAAC,IAAK9C,cAAc,CAAC8C,CAAC,CAACC,MAAM,CAACG,OAAO,CAAE;UAClDd,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACFjD,OAAA;UAAO2D,OAAO,EAAC,aAAa;UAACf,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEjD,OAAA;IAAK4C,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtC7C,OAAA;MAAQ4C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC7D7C,OAAA;QAAK4C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD7C,OAAA;UAAK4C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD7C,OAAA;YAAK4C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C7C,OAAA;cAAK4C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,eAC5C7C,OAAA,CAACP,UAAU;gBAACmD,SAAS,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNjD,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAI4C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEjD,OAAA;gBAAG4C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAK4C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAE1C7C,OAAA;cAAK4C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C7C,OAAA,CAACN,IAAI;gBACHkE,IAAI,EAAE,EAAG;gBACThB,SAAS,EAAE1B,WAAW,GAAG,kBAAkB,GAAG;cAAgB;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACFjD,OAAA;gBAAM4C,SAAS,EAAE,WAAW1B,WAAW,GAAG,kBAAkB,GAAG,eAAe,EAAG;gBAAA2B,QAAA,EAC9E3B,WAAW,GAAG,WAAW,GAAG;cAAc;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNjD,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,CAACD,YAAY,CAAE;cAC9CuC,SAAS,EAAC,sFAAsF;cAAAC,QAAA,eAEhG7C,OAAA,CAACR,QAAQ;gBAACoE,IAAI,EAAE;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTjD,OAAA;MAAM4C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,GAE1DxC,YAAY,iBAAIL,OAAA,CAAC2C,aAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGlCjD,OAAA;QAAK4C,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B7C,OAAA;UAAK4C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAI4C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EjD,OAAA;cAAG4C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACjC3B,WAAW,GAAG,gBAAgBf,KAAK,EAAE,GAAG,qBAAqBA,KAAK;YAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNjD,OAAA;YACEkD,OAAO,EAAEX,sBAAuB;YAChCsB,QAAQ,EAAEzC,YAAa;YACvBwB,SAAS,EAAE,0GACT1B,WAAW,GACP,oEAAoE,GACpE,uEAAuE,IACzEE,YAAY,GAAG,+BAA+B,GAAG,EAAE,EAAG;YAAAyB,QAAA,EAEzDzB,YAAY,GAAG,eAAe,GAAGF,WAAW,GAAG,YAAY,GAAG;UAAS;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjD,OAAA;QAAK4C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD7C,OAAA;UAAK4C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC7C,OAAA,CAACH,YAAY;YACXa,SAAS,EAAEA,SAAU;YACrBQ,WAAW,EAAEA,WAAY;YACzBM,OAAO,EAAEA,OAAQ;YACjBsC,WAAW,EAAEtB,eAAgB;YAC7BuB,SAAS,EAAEtB,aAAc;YACzBuB,YAAY,EAAEtB,gBAAiB;YAC/BjB,UAAU,EAAEA;UAAW;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjD,OAAA;UAAK4C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC7C,OAAA,CAACF,UAAU;YACTW,eAAe,EAAEA,eAAgB;YACjCC,SAAS,EAAEA,SAAU;YACrBa,QAAQ,EAAEA,QAAS;YACnBX,SAAS,EAAEA,SAAU;YACrBD,QAAQ,EAAEA,QAAS;YACnBc,UAAU,EAAEA;UAAW;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLpB,WAAW,iBACV7B,OAAA;QAAK4C,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D7C,OAAA;UAAK4C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC7C,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B7C,OAAA;cAAK4C,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClF7C,OAAA,CAACR,QAAQ;gBAACoE,IAAI,EAAE,EAAG;gBAAChB,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjD,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAI4C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjD,OAAA;cAAG4C,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAE5C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJjD,OAAA;cACEkD,OAAO,EAAEhB,iBAAkB;cAC3BU,SAAS,EAAC,8DAA8D;cAAAC,QAAA,EACzE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAhB,WAAW,iBACVjC,OAAA;QAAK4C,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D7C,OAAA;UAAK4C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC7C,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B7C,OAAA;cAAK4C,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClF7C,OAAA,CAACR,QAAQ;gBAACoE,IAAI,EAAE,EAAG;gBAAChB,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjD,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAI4C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjD,OAAA;cAAG4C,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAE5C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDjD,OAAA;QAAK4C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7C,OAAA;UAAI4C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEjD,OAAA;UAAK4C,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9C7C,OAAA;YAAK4C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC7C,OAAA;cAAM4C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJjD,OAAA;cAAA6C,QAAA,EAAG;YAAwE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eACNjD,OAAA;YAAK4C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC7C,OAAA;cAAM4C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJjD,OAAA;cAAA6C,QAAA,EAAG;YAAqD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNjD,OAAA;YAAK4C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC7C,OAAA;cAAM4C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJjD,OAAA;cAAA6C,QAAA,EAAG;YAAkD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNjD,OAAA;YAAK4C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC7C,OAAA;cAAM4C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJjD,OAAA;cAAA6C,QAAA,EAAG;YAAuD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNjD,OAAA;YAAK4C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC7C,OAAA;cAAM4C,SAAS,EAAC,yHAAyH;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClJjD,OAAA;cAAA6C,QAAA,EAAG;YAA0C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPjD,OAAA,CAACT,OAAO;MACN0E,QAAQ,EAAC,WAAW;MACpBC,YAAY,EAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;UACLC,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE;QACT,CAAC;QACDC,OAAO,EAAE;UACPJ,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF,CAAC;QACDC,KAAK,EAAE;UACLR,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF;MACF;IAAE;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAC/C,EAAA,CAjUQD,GAAG;EAAA,QAmBNN,cAAc,EAoBdC,aAAa;AAAA;AAAAgF,EAAA,GAvCV3E,GAAG;AAmUZ,eAAeA,GAAG;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}